<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MobilizationParticipation extends Model
{
    use HasFactory;

    protected $table = 'participaciones_movilizacion';

    protected $fillable = [
        'persona_id',
        'movilizacion_id',
        'estado_participacion',
        'fecha_confirmacion',
        'fecha_asistencia',
        'observaciones',
    ];

    protected $casts = [
        'fecha_confirmacion' => 'datetime',
        'fecha_asistencia' => 'datetime',
    ];

    /**
     * Relationship with person
     */
    public function person(): BelongsTo
    {
        return $this->belongsTo(Person::class, 'persona_id');
    }

    /**
     * Relationship with mobilization
     */
    public function mobilization(): BelongsTo
    {
        return $this->belongsTo(Mobilization::class, 'movilizacion_id');
    }

    // Legacy compatibility methods
    public function persona(): BelongsTo
    {
        return $this->person();
    }

    public function movilizacion(): BelongsTo
    {
        return $this->mobilization();
    }
}
