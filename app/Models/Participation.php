<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Participation extends Model
{
    use HasFactory;

    protected $table = 'participaciones';

    protected $fillable = [
        'persona_id',
        'evento_electoral_id',
        'tipo_participacion',
        'confirmo_participacion',
        'fecha_confirmacion',
        'observaciones',
    ];

    protected $casts = [
        'confirmo_participacion' => 'boolean',
        'fecha_confirmacion' => 'datetime',
    ];

    /**
     * Relationship with person
     */
    public function person(): BelongsTo
    {
        return $this->belongsTo(Person::class, 'persona_id');
    }

    /**
     * Relationship with electoral event
     */
    public function electoralEvent(): BelongsTo
    {
        return $this->belongsTo(ElectoralEvent::class, 'evento_electoral_id');
    }

    // Legacy compatibility methods
    public function persona(): BelongsTo
    {
        return $this->person();
    }

    public function eventoElectoral(): BelongsTo
    {
        return $this->electoralEvent();
    }
}
