<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class State extends Model
{
    use HasFactory;

    protected $table = 'estados';

    protected $fillable = [
        'nombre',
        'codigo',
        'activo',
    ];

    protected $casts = [
        'activo' => 'boolean',
    ];

    /**
     * Relationship with municipalities
     */
    public function municipalities(): HasMany
    {
        return $this->hasMany(Municipality::class, 'estado_id');
    }

    /**
     * Relationship with people
     */
    public function people(): HasMany
    {
        return $this->hasMany(Person::class, 'estado_id');
    }

    /**
     * Scope for active states
     */
    public function scopeActive($query)
    {
        return $query->where('activo', true);
    }

    // Legacy compatibility methods
    public function municipios(): HasMany
    {
        return $this->municipalities();
    }

    public function personas(): HasMany
    {
        return $this->people();
    }

    public function scopeActivos($query)
    {
        return $this->scopeActive($query);
    }
}
