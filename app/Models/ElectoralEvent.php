<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class ElectoralEvent extends Model
{
    use HasFactory;

    protected $table = 'eventos_electorales';

    protected $fillable = [
        'nombre',
        'descripcion',
        'fecha_evento',
        'tipo',
        'estado',
    ];

    protected $casts = [
        'fecha_evento' => 'date',
    ];

    /**
     * Relationship with participations
     */
    public function participations(): HasMany
    {
        return $this->hasMany(Participation::class, 'evento_electoral_id');
    }

    /**
     * Relationship with people
     */
    public function people(): BelongsToMany
    {
        return $this->belongsToMany(Person::class, 'participaciones', 'evento_electoral_id', 'persona_id')
            ->withPivot(['tipo_participacion', 'confirmo_participacion', 'fecha_confirmacion', 'observaciones'])
            ->withTimestamps();
    }

    /**
     * Scope for active events
     */
    public function scopeActive($query)
    {
        return $query->whereIn('estado', ['programado', 'en_curso']);
    }

    // Legacy compatibility methods
    public function participaciones(): HasMany
    {
        return $this->participations();
    }

    public function personas(): BelongsToMany
    {
        return $this->people();
    }

    public function scopeActivos($query)
    {
        return $this->scopeActive($query);
    }
}
