<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Person extends Model
{
    use HasFactory;

    protected $table = 'personas';

    protected $fillable = [
        'nombres',
        'apellidos',
        'cedula',
        'fecha_nacimiento',
        'genero',
        'telefono',
        'telefono_secundario',
        'email',
        'direccion',
        'estado_id',
        'municipio_id',
        'parroquia_id',
        'centro_votacion_id',
        'mesa_votacion',
        'tipo_persona',
        'es_lider_1x10',
        'lider_asignado_id',
        'user_id',
        'estado',
        'observaciones',
        'datos_adicionales',
    ];

    protected $casts = [
        'fecha_nacimiento' => 'date',
        'es_lider_1x10' => 'boolean',
        'datos_adicionales' => 'array',
    ];

    /**
     * Relationship with geographic state
     */
    public function state(): BelongsTo
    {
        return $this->belongsTo(Estado::class, 'estado_id');
    }

    /**
     * Relationship with municipality
     */
    public function municipality(): BelongsTo
    {
        return $this->belongsTo(Municipio::class, 'municipio_id');
    }

    /**
     * Relationship with parish
     */
    public function parish(): BelongsTo
    {
        return $this->belongsTo(Parroquia::class, 'parroquia_id');
    }

    /**
     * Relationship with voting center
     */
    public function votingCenter(): BelongsTo
    {
        return $this->belongsTo(CentroVotacion::class, 'centro_votacion_id');
    }

    /**
     * Relationship with assigned leader
     */
    public function assignedLeader(): BelongsTo
    {
        return $this->belongsTo(Person::class, 'lider_asignado_id');
    }

    /**
     * Relationship with assigned people (for leaders)
     */
    public function assignedPeople(): HasMany
    {
        return $this->hasMany(Person::class, 'lider_asignado_id');
    }

    /**
     * Relationship with system user
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Relationship with electoral events
     */
    public function electoralEvents(): BelongsToMany
    {
        return $this->belongsToMany(ElectoralEvent::class, 'participations')
            ->withPivot(['participation_type', 'confirmed_participation', 'confirmation_date', 'notes'])
            ->withTimestamps();
    }

    /**
     * Relationship with mobilizations
     */
    public function mobilizations(): BelongsToMany
    {
        return $this->belongsToMany(Mobilization::class, 'mobilization_participations')
            ->withPivot(['participation_status', 'confirmation_date', 'attendance_date', 'notes'])
            ->withTimestamps();
    }

    /**
     * Accessor for full name
     */
    public function getFullNameAttribute(): string
    {
        return trim($this->nombres . ' ' . $this->apellidos);
    }

    /**
     * Accessor for age
     */
    public function getAgeAttribute(): ?int
    {
        return $this->fecha_nacimiento ? $this->fecha_nacimiento->age : null;
    }

    /**
     * Scope for active people
     */
    public function scopeActive($query)
    {
        return $query->where('estado', 'activo');
    }

    /**
     * Scope for militants
     */
    public function scopeMilitants($query)
    {
        return $query->where('tipo_persona', 'militante');
    }

    /**
     * Scope for voters
     */
    public function scopeVoters($query)
    {
        return $query->where('tipo_persona', 'votante');
    }

    /**
     * Scope for sympathizers
     */
    public function scopeSympathizers($query)
    {
        return $query->where('tipo_persona', 'simpatizante');
    }

    /**
     * Scope for 1x10 leaders
     */
    public function scopeLeaders1x10($query)
    {
        return $query->where('es_lider_1x10', true);
    }

    /**
     * Scope for text search
     */
    public function scopeSearchText($query, $text)
    {
        return $query->where(function ($q) use ($text) {
            $q->where('nombres', 'like', "%{$text}%")
              ->orWhere('apellidos', 'like', "%{$text}%")
              ->orWhere('cedula', 'like', "%{$text}%")
              ->orWhere('email', 'like', "%{$text}%")
              ->orWhere('telefono', 'like', "%{$text}%");
        });
    }

    /**
     * Check if can be 1x10 leader
     */
    public function canBe1x10Leader(): bool
    {
        return $this->assignedPeople()->count() < 10;
    }

    /**
     * Get available spaces for 1x10 leader
     */
    public function getAvailableSpaces(): int
    {
        return 10 - $this->assignedPeople()->count();
    }

    /**
     * Check if has system user
     */
    public function hasSystemUser(): bool
    {
        return !is_null($this->user_id);
    }

    /**
     * Get person type in Spanish for compatibility
     */
    public function getPersonTypeSpanishAttribute(): string
    {
        $types = [
            'militant' => 'militante',
            'voter' => 'votante',
            'sympathizer' => 'simpatizante',
        ];

        return $types[$this->person_type] ?? $this->person_type;
    }

    /**
     * Get status in Spanish for compatibility
     */
    public function getStatusSpanishAttribute(): string
    {
        $statuses = [
            'active' => 'activo',
            'inactive' => 'inactivo',
            'suspended' => 'suspendido',
        ];

        return $statuses[$this->status] ?? $this->status;
    }
}
