<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Municipality extends Model
{
    use HasFactory;

    protected $table = 'municipios';

    protected $fillable = [
        'estado_id',
        'nombre',
        'codigo',
        'activo',
    ];

    protected $casts = [
        'activo' => 'boolean',
    ];

    /**
     * Relationship with state
     */
    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class, 'estado_id');
    }

    /**
     * Relationship with parishes
     */
    public function parishes(): HasMany
    {
        return $this->hasMany(Parish::class, 'municipio_id');
    }

    /**
     * Relationship with people
     */
    public function people(): HasMany
    {
        return $this->hasMany(Person::class, 'municipio_id');
    }

    /**
     * Scope for active municipalities
     */
    public function scopeActive($query)
    {
        return $query->where('activo', true);
    }

    // Legacy compatibility methods
    public function estado(): BelongsTo
    {
        return $this->state();
    }

    public function parroquias(): Has<PERSON>any
    {
        return $this->parishes();
    }

    public function personas(): HasMany
    {
        return $this->people();
    }

    public function scopeActivos($query)
    {
        return $this->scopeActive($query);
    }
}
