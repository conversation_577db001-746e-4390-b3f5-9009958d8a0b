<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class VotingCenter extends Model
{
    use HasFactory;

    protected $table = 'centros_votacion';

    protected $fillable = [
        'parroquia_id',
        'nombre',
        'codigo',
        'direccion',
        'activo',
    ];

    protected $casts = [
        'activo' => 'boolean',
    ];

    /**
     * Relationship with parish
     */
    public function parish(): BelongsTo
    {
        return $this->belongsTo(Parish::class, 'parroquia_id');
    }

    /**
     * Relationship with people
     */
    public function people(): Has<PERSON>any
    {
        return $this->hasMany(Person::class, 'centro_votacion_id');
    }

    /**
     * Scope for active voting centers
     */
    public function scopeActive($query)
    {
        return $query->where('activo', true);
    }

    // Legacy compatibility methods
    public function parroquia(): BelongsTo
    {
        return $this->parish();
    }

    public function personas(): HasMany
    {
        return $this->people();
    }

    public function scopeActivos($query)
    {
        return $this->scopeActive($query);
    }
}
