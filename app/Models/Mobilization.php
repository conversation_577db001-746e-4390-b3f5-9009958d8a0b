<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Mobilization extends Model
{
    use HasFactory;

    protected $table = 'movilizaciones';

    protected $fillable = [
        'nombre',
        'descripcion',
        'fecha_inicio',
        'fecha_fin',
        'tipo',
        'estado',
        'ubicacion',
        'meta_participantes',
    ];

    protected $casts = [
        'fecha_inicio' => 'datetime',
        'fecha_fin' => 'datetime',
    ];

    /**
     * Relationship with mobilization participations
     */
    public function mobilizationParticipations(): Has<PERSON>any
    {
        return $this->hasMany(MobilizationParticipation::class, 'movilizacion_id');
    }

    /**
     * Relationship with people
     */
    public function people(): BelongsToMany
    {
        return $this->belongsToMany(Person::class, 'participaciones_movilizacion', 'movilizacion_id', 'persona_id')
            ->withPivot(['estado_participacion', 'fecha_confirmacion', 'fecha_asistencia', 'observaciones'])
            ->withTimestamps();
    }

    /**
     * Scope for active mobilizations
     */
    public function scopeActive($query)
    {
        return $query->whereIn('estado', ['planificada', 'en_curso']);
    }

    // Legacy compatibility methods
    public function participacionesMovilizacion(): HasMany
    {
        return $this->mobilizationParticipations();
    }

    public function personas(): BelongsToMany
    {
        return $this->people();
    }

    public function scopeActivas($query)
    {
        return $this->scopeActive($query);
    }
}
