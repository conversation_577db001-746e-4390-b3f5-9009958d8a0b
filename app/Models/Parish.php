<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Parish extends Model
{
    use HasFactory;

    protected $table = 'parroquias';

    protected $fillable = [
        'municipio_id',
        'nombre',
        'codigo',
        'activo',
    ];

    protected $casts = [
        'activo' => 'boolean',
    ];

    /**
     * Relationship with municipality
     */
    public function municipality(): BelongsTo
    {
        return $this->belongsTo(Municipality::class, 'municipio_id');
    }

    /**
     * Relationship with voting centers
     */
    public function votingCenters(): HasMany
    {
        return $this->hasMany(VotingCenter::class, 'parroquia_id');
    }

    /**
     * Relationship with people
     */
    public function people(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Person::class, 'parroquia_id');
    }

    /**
     * Scope for active parishes
     */
    public function scopeActive($query)
    {
        return $query->where('activo', true);
    }

    // Legacy compatibility methods
    public function municipio(): BelongsTo
    {
        return $this->municipality();
    }

    public function centrosVotacion(): HasMany
    {
        return $this->votingCenters();
    }

    public function personas(): HasMany
    {
        return $this->people();
    }

    public function scopeActivos($query)
    {
        return $this->scopeActive($query);
    }
}
