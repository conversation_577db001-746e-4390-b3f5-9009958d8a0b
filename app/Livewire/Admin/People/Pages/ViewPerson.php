<?php

namespace App\Livewire\Admin\People\Pages;

use App\Models\Person;
use Illuminate\Contracts\View\View;
use Livewire\Attributes\Layout;
use Livewire\Component;

class ViewPerson extends Component
{
    public Person $person;

    public function mount(Person $person): void
    {
        $this->authorize('view people');
        $this->person = $person;
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        return view('livewire.admin.people.pages.view-person');
    }
}
