<?php

namespace App\Livewire\Admin\People\Pages;

use App\Models\Person;
use App\Models\Estado;
use App\Models\Municipio;
use App\Models\Parroquia;
use Illuminate\Contracts\View\View;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Session;
use Livewire\Attributes\Url;
use Livewire\Component;
use Livewire\WithPagination;

class People extends Component
{
    use LivewireAlert;
    use WithPagination;

    /** @var array<string,string> */
    protected $listeners = [
        'personDeleted' => '$refresh',
        'personCreated' => '$refresh',
        'personUpdated' => '$refresh',
    ];

    #[Session]
    public int $perPage = 15;

    /** @var array<int,string> */
    public array $searchableFields = ['nombres', 'apellidos', 'cedula', 'email', 'telefono'];

    #[Url]
    public string $search = '';

    #[Url]
    public string $personType = '';

    #[Url]
    public string $personStatus = '';

    #[Url]
    public ?int $stateId = null;

    #[Url]
    public ?int $municipalityId = null;

    #[Url]
    public ?int $parishId = null;

    #[Url]
    public string $isLeader = '';

    #[Url]
    public string $activeTab = 'all';

    public function mount(): void
    {
        $this->authorize('view people');
    }

    public function updatingSearch(): void
    {
        $this->resetPage();
    }

    public function updatingPersonType(): void
    {
        $this->resetPage();
    }

    public function updatingPersonStatus(): void
    {
        $this->resetPage();
    }

    public function updatingStateId(): void
    {
        $this->municipalityId = null;
        $this->parishId = null;
        $this->resetPage();
    }

    public function updatingMunicipalityId(): void
    {
        $this->parishId = null;
        $this->resetPage();
    }

    public function updatingParishId(): void
    {
        $this->resetPage();
    }

    public function updatingActiveTab(): void
    {
        $this->resetPage();

        // Clear specific filters when changing tabs
        $this->personType = '';
        $this->isLeader = '';
    }

    public function setActiveTab(string $tab): void
    {
        $this->activeTab = $tab;
        $this->resetPage();

        // Adjust filters according to tab
        switch ($tab) {
            case 'militants':
                $this->personType = 'militant';
                break;
            case 'voters':
                $this->personType = 'voter';
                break;
            case 'sympathizers':
                $this->personType = 'sympathizer';
                break;
            case 'leaders':
                $this->isLeader = '1';
                $this->personType = '';
                break;
            default:
                $this->personType = '';
                $this->isLeader = '';
        }
    }

    public function clearFilters(): void
    {
        $this->search = '';
        $this->personType = '';
        $this->personStatus = '';
        $this->stateId = null;
        $this->municipalityId = null;
        $this->parishId = null;
        $this->isLeader = '';
        $this->activeTab = 'all';
        $this->resetPage();
    }

    public function deletePerson(int $personId): void
    {
        $this->authorize('delete people');

        $person = Person::findOrFail($personId);

        // Check if has assigned people
        if ($person->assignedPeople()->count() > 0) {
            $this->alert('error', 'Cannot delete a person who has other people assigned');
            return;
        }

        $person->delete();

        $this->alert('success', __('people.person_deleted'));
        $this->dispatch('personDeleted');
    }

    public function exportPeople(): void
    {
        $this->authorize('export people');

        // Here you would implement the export logic
        $this->alert('info', __('people.export_in_development'));
    }

    public function getMunicipalitiesProperty()
    {
        if (!$this->stateId) {
            return collect();
        }

        return Municipio::where('estado_id', $this->stateId)
            ->activos()
            ->orderBy('nombre')
            ->get();
    }

    public function getParishesProperty()
    {
        if (!$this->municipalityId) {
            return collect();
        }

        return Parroquia::where('municipio_id', $this->municipalityId)
            ->activos()
            ->orderBy('nombre')
            ->get();
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        $query = Person::query()
            ->with(['state', 'municipality', 'parish', 'votingCenter', 'assignedLeader', 'user'])
            ->when($this->search, function ($query, $search) {
                $query->searchText($search);
            })
            ->when($this->personStatus, function ($query, $status) {
                $query->where('estado', $status);
            })
            ->when($this->stateId, function ($query, $stateId) {
                $query->where('estado_id', $stateId);
            })
            ->when($this->municipalityId, function ($query, $municipalityId) {
                $query->where('municipio_id', $municipalityId);
            })
            ->when($this->parishId, function ($query, $parishId) {
                $query->where('parroquia_id', $parishId);
            });

        // Apply filters according to active tab
        switch ($this->activeTab) {
            case 'militants':
                $query->where('tipo_persona', 'militante');
                break;
            case 'voters':
                $query->where('tipo_persona', 'votante');
                break;
            case 'sympathizers':
                $query->where('tipo_persona', 'simpatizante');
                break;
            case 'leaders':
                $query->where('es_lider_1x10', true);
                break;
            default:
                // For "all" tab, apply additional filters if defined
                if ($this->personType) {
                    $query->where('tipo_persona', $this->personType);
                }
                if ($this->isLeader === '1') {
                    $query->where('es_lider_1x10', true);
                } elseif ($this->isLeader === '0') {
                    $query->where('es_lider_1x10', false);
                }
        }

        $people = $query->orderBy('nombres')
            ->orderBy('apellidos')
            ->paginate($this->perPage);

        // Statistics for tabs
        $stats = [
            'total' => Person::count(),
            'militants' => Person::militants()->count(),
            'voters' => Person::voters()->count(),
            'sympathizers' => Person::sympathizers()->count(),
            'leaders' => Person::leaders1x10()->count(),
        ];

        return view('livewire.admin.people.pages.people', [
            'people' => $people,
            'states' => Estado::activos()->orderBy('nombre')->get(),
            'municipalities' => $this->municipalities,
            'parishes' => $this->parishes,
            'stats' => $stats,
        ]);
    }
}
