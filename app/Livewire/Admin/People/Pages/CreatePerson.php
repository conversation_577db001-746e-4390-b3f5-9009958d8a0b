<?php

namespace App\Livewire\Admin\People\Pages;

use Illuminate\Contracts\View\View;
use Livewire\Attributes\Layout;
use Livewire\Component;

class Create<PERSON>erson extends Component
{
    public function mount(): void
    {
        $this->authorize('create people');
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        return view('livewire.admin.people.pages.create-person');
    }
}
