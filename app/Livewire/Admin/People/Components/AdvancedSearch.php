<?php

namespace App\Livewire\Admin\People\Components;

use Illuminate\Contracts\View\View;
use Livewire\Attributes\Layout;
use Livewire\Component;

class AdvancedSearch extends Component
{
    public function mount(): void
    {
        $this->authorize('view people');
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        return view('livewire.admin.people.components.advanced-search');
    }
}
