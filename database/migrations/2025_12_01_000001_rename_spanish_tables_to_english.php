<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Step 1: Rename tables from Spanish to English
        Schema::rename('estados', 'states');
        Schema::rename('municipios', 'municipalities');
        Schema::rename('parroquias', 'parishes');
        Schema::rename('centros_votacion', 'voting_centers');
        Schema::rename('eventos_electorales', 'electoral_events');
        Schema::rename('participaciones', 'participations');
        Schema::rename('movilizaciones', 'mobilizations');
        Schema::rename('participaciones_movilizacion', 'mobilization_participations');

        // Step 2: Update column names in states table
        Schema::table('states', function (Blueprint $table) {
            $table->renameColumn('nombre', 'name');
            $table->renameColumn('codigo', 'code');
            $table->renameColumn('activo', 'active');
        });

        // Step 3: Update column names in municipalities table
        Schema::table('municipalities', function (Blueprint $table) {
            $table->renameColumn('estado_id', 'state_id');
            $table->renameColumn('nombre', 'name');
            $table->renameColumn('codigo', 'code');
            $table->renameColumn('activo', 'active');
        });

        // Step 4: Update column names in parishes table
        Schema::table('parishes', function (Blueprint $table) {
            $table->renameColumn('municipio_id', 'municipality_id');
            $table->renameColumn('nombre', 'name');
            $table->renameColumn('codigo', 'code');
            $table->renameColumn('activo', 'active');
        });

        // Step 5: Update column names in voting_centers table
        Schema::table('voting_centers', function (Blueprint $table) {
            $table->renameColumn('parroquia_id', 'parish_id');
            $table->renameColumn('nombre', 'name');
            $table->renameColumn('codigo', 'code');
            $table->renameColumn('direccion', 'address');
            $table->renameColumn('activo', 'active');
        });

        // Step 6: Update column names in electoral_events table
        Schema::table('electoral_events', function (Blueprint $table) {
            $table->renameColumn('nombre', 'name');
            $table->renameColumn('descripcion', 'description');
            $table->renameColumn('fecha_evento', 'event_date');
            $table->renameColumn('tipo', 'type');
            $table->renameColumn('estado', 'status');
        });

        // Step 7: Update enum values in electoral_events table
        DB::statement("UPDATE electoral_events SET type = CASE
            WHEN type = 'eleccion' THEN 'election'
            WHEN type = 'referendum' THEN 'referendum'
            WHEN type = 'consulta' THEN 'consultation'
            WHEN type = 'primaria' THEN 'primary'
            ELSE type END");

        DB::statement("UPDATE electoral_events SET status = CASE
            WHEN status = 'programado' THEN 'scheduled'
            WHEN status = 'en_curso' THEN 'in_progress'
            WHEN status = 'finalizado' THEN 'completed'
            WHEN status = 'cancelado' THEN 'cancelled'
            ELSE status END");

        // Step 8: Update column names in participations table
        Schema::table('participations', function (Blueprint $table) {
            $table->renameColumn('persona_id', 'person_id');
            $table->renameColumn('evento_electoral_id', 'electoral_event_id');
            $table->renameColumn('tipo_participacion', 'participation_type');
            $table->renameColumn('confirmo_participacion', 'confirmed_participation');
            $table->renameColumn('fecha_confirmacion', 'confirmation_date');
            $table->renameColumn('observaciones', 'notes');
        });

        // Step 9: Update enum values in participations table
        DB::statement("UPDATE participations SET participation_type = CASE
            WHEN participation_type = 'voto' THEN 'vote'
            WHEN participation_type = 'testigo' THEN 'witness'
            WHEN participation_type = 'coordinador' THEN 'coordinator'
            WHEN participation_type = 'movilizador' THEN 'mobilizer'
            ELSE participation_type END");

        // Step 10: Update column names in mobilizations table
        Schema::table('mobilizations', function (Blueprint $table) {
            $table->renameColumn('nombre', 'name');
            $table->renameColumn('descripcion', 'description');
            $table->renameColumn('fecha_inicio', 'start_date');
            $table->renameColumn('fecha_fin', 'end_date');
            $table->renameColumn('tipo', 'type');
            $table->renameColumn('estado', 'status');
            $table->renameColumn('ubicacion', 'location');
            $table->renameColumn('meta_participantes', 'target_participants');
        });

        // Step 11: Update enum values in mobilizations table
        DB::statement("UPDATE mobilizations SET type = CASE
            WHEN type = 'evento' THEN 'event'
            WHEN type = 'campana' THEN 'campaign'
            WHEN type = 'reunion' THEN 'meeting'
            WHEN type = 'capacitacion' THEN 'training'
            ELSE type END");

        DB::statement("UPDATE mobilizations SET status = CASE
            WHEN status = 'planificada' THEN 'planned'
            WHEN status = 'en_curso' THEN 'in_progress'
            WHEN status = 'finalizada' THEN 'completed'
            WHEN status = 'cancelada' THEN 'cancelled'
            ELSE status END");

        // Step 12: Update column names in mobilization_participations table
        Schema::table('mobilization_participations', function (Blueprint $table) {
            $table->renameColumn('persona_id', 'person_id');
            $table->renameColumn('movilizacion_id', 'mobilization_id');
            $table->renameColumn('estado_participacion', 'participation_status');
            $table->renameColumn('fecha_confirmacion', 'confirmation_date');
            $table->renameColumn('fecha_asistencia', 'attendance_date');
            $table->renameColumn('observaciones', 'notes');
        });

        // Step 13: Update enum values in mobilization_participations table
        DB::statement("UPDATE mobilization_participations SET participation_status = CASE
            WHEN participation_status = 'invitado' THEN 'invited'
            WHEN participation_status = 'confirmado' THEN 'confirmed'
            WHEN participation_status = 'asistio' THEN 'attended'
            WHEN participation_status = 'no_asistio' THEN 'not_attended'
            ELSE participation_status END");

        // Step 14: Update foreign key constraints to reference new table names
        Schema::table('municipalities', function (Blueprint $table) {
            $table->dropForeign(['state_id']);
            $table->foreign('state_id')->references('id')->on('states')->onDelete('cascade');
        });

        Schema::table('parishes', function (Blueprint $table) {
            $table->dropForeign(['municipality_id']);
            $table->foreign('municipality_id')->references('id')->on('municipalities')->onDelete('cascade');
        });

        Schema::table('voting_centers', function (Blueprint $table) {
            $table->dropForeign(['parish_id']);
            $table->foreign('parish_id')->references('id')->on('parishes')->onDelete('cascade');
        });

        Schema::table('people', function (Blueprint $table) {
            $table->dropForeign(['state_id']);
            $table->dropForeign(['municipality_id']);
            $table->dropForeign(['parish_id']);
            $table->dropForeign(['voting_center_id']);
            $table->dropForeign(['assigned_leader_id']);

            $table->foreign('state_id')->references('id')->on('states')->onDelete('set null');
            $table->foreign('municipality_id')->references('id')->on('municipalities')->onDelete('set null');
            $table->foreign('parish_id')->references('id')->on('parishes')->onDelete('set null');
            $table->foreign('voting_center_id')->references('id')->on('voting_centers')->onDelete('set null');
            $table->foreign('assigned_leader_id')->references('id')->on('people')->onDelete('set null');
        });

        Schema::table('participations', function (Blueprint $table) {
            $table->dropForeign(['person_id']);
            $table->dropForeign(['electoral_event_id']);

            $table->foreign('person_id')->references('id')->on('people')->onDelete('cascade');
            $table->foreign('electoral_event_id')->references('id')->on('electoral_events')->onDelete('cascade');
        });

        Schema::table('mobilization_participations', function (Blueprint $table) {
            $table->dropForeign(['person_id']);
            $table->dropForeign(['mobilization_id']);

            $table->foreign('person_id')->references('id')->on('people')->onDelete('cascade');
            $table->foreign('mobilization_id')->references('id')->on('mobilizations')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Reverse enum values in mobilization_participations table
        DB::statement("UPDATE mobilization_participations SET participation_status = CASE
            WHEN participation_status = 'invited' THEN 'invitado'
            WHEN participation_status = 'confirmed' THEN 'confirmado'
            WHEN participation_status = 'attended' THEN 'asistio'
            WHEN participation_status = 'not_attended' THEN 'no_asistio'
            ELSE participation_status END");

        // Reverse column names in mobilization_participations table
        Schema::table('mobilization_participations', function (Blueprint $table) {
            $table->renameColumn('person_id', 'persona_id');
            $table->renameColumn('mobilization_id', 'movilizacion_id');
            $table->renameColumn('participation_status', 'estado_participacion');
            $table->renameColumn('confirmation_date', 'fecha_confirmacion');
            $table->renameColumn('attendance_date', 'fecha_asistencia');
            $table->renameColumn('notes', 'observaciones');
        });

        // Reverse enum values in mobilizations table
        DB::statement("UPDATE mobilizations SET status = CASE
            WHEN status = 'planned' THEN 'planificada'
            WHEN status = 'in_progress' THEN 'en_curso'
            WHEN status = 'completed' THEN 'finalizada'
            WHEN status = 'cancelled' THEN 'cancelada'
            ELSE status END");

        DB::statement("UPDATE mobilizations SET type = CASE
            WHEN type = 'event' THEN 'evento'
            WHEN type = 'campaign' THEN 'campana'
            WHEN type = 'meeting' THEN 'reunion'
            WHEN type = 'training' THEN 'capacitacion'
            ELSE type END");

        // Reverse column names in mobilizations table
        Schema::table('mobilizations', function (Blueprint $table) {
            $table->renameColumn('name', 'nombre');
            $table->renameColumn('description', 'descripcion');
            $table->renameColumn('start_date', 'fecha_inicio');
            $table->renameColumn('end_date', 'fecha_fin');
            $table->renameColumn('type', 'tipo');
            $table->renameColumn('status', 'estado');
            $table->renameColumn('location', 'ubicacion');
            $table->renameColumn('target_participants', 'meta_participantes');
        });

        // Reverse enum values in participations table
        DB::statement("UPDATE participations SET participation_type = CASE
            WHEN participation_type = 'vote' THEN 'voto'
            WHEN participation_type = 'witness' THEN 'testigo'
            WHEN participation_type = 'coordinator' THEN 'coordinador'
            WHEN participation_type = 'mobilizer' THEN 'movilizador'
            ELSE participation_type END");

        // Reverse column names in participations table
        Schema::table('participations', function (Blueprint $table) {
            $table->renameColumn('person_id', 'persona_id');
            $table->renameColumn('electoral_event_id', 'evento_electoral_id');
            $table->renameColumn('participation_type', 'tipo_participacion');
            $table->renameColumn('confirmed_participation', 'confirmo_participacion');
            $table->renameColumn('confirmation_date', 'fecha_confirmacion');
            $table->renameColumn('notes', 'observaciones');
        });

        // Reverse enum values in electoral_events table
        DB::statement("UPDATE electoral_events SET status = CASE
            WHEN status = 'scheduled' THEN 'programado'
            WHEN status = 'in_progress' THEN 'en_curso'
            WHEN status = 'completed' THEN 'finalizado'
            WHEN status = 'cancelled' THEN 'cancelado'
            ELSE status END");

        DB::statement("UPDATE electoral_events SET type = CASE
            WHEN type = 'election' THEN 'eleccion'
            WHEN type = 'referendum' THEN 'referendum'
            WHEN type = 'consultation' THEN 'consulta'
            WHEN type = 'primary' THEN 'primaria'
            ELSE type END");

        // Reverse column names in electoral_events table
        Schema::table('electoral_events', function (Blueprint $table) {
            $table->renameColumn('name', 'nombre');
            $table->renameColumn('description', 'descripcion');
            $table->renameColumn('event_date', 'fecha_evento');
            $table->renameColumn('type', 'tipo');
            $table->renameColumn('status', 'estado');
        });

        // Reverse column names in voting_centers table
        Schema::table('voting_centers', function (Blueprint $table) {
            $table->renameColumn('parish_id', 'parroquia_id');
            $table->renameColumn('name', 'nombre');
            $table->renameColumn('code', 'codigo');
            $table->renameColumn('address', 'direccion');
            $table->renameColumn('active', 'activo');
        });

        // Reverse column names in parishes table
        Schema::table('parishes', function (Blueprint $table) {
            $table->renameColumn('municipality_id', 'municipio_id');
            $table->renameColumn('name', 'nombre');
            $table->renameColumn('code', 'codigo');
            $table->renameColumn('active', 'activo');
        });

        // Reverse column names in municipalities table
        Schema::table('municipalities', function (Blueprint $table) {
            $table->renameColumn('state_id', 'estado_id');
            $table->renameColumn('name', 'nombre');
            $table->renameColumn('code', 'codigo');
            $table->renameColumn('active', 'activo');
        });

        // Reverse column names in states table
        Schema::table('states', function (Blueprint $table) {
            $table->renameColumn('name', 'nombre');
            $table->renameColumn('code', 'codigo');
            $table->renameColumn('active', 'activo');
        });

        // Reverse table names from English to Spanish
        Schema::rename('mobilization_participations', 'participaciones_movilizacion');
        Schema::rename('mobilizations', 'movilizaciones');
        Schema::rename('participations', 'participaciones');
        Schema::rename('electoral_events', 'eventos_electorales');
        Schema::rename('voting_centers', 'centros_votacion');
        Schema::rename('parishes', 'parroquias');
        Schema::rename('municipalities', 'municipios');
        Schema::rename('states', 'estados');
    }
};
